# 合肥文旅助手

一个功能完整的合肥市文旅助手移动端应用，提供智能路径规划、伴游导览、游记生成等服务。

## 功能特色

### 🏠 首页
- **Banner轮播**: 顶部直接展示骆岗公园、环巢湖片区、庐江山水片区三大生态区域
- **智能服务**: 路径规划、伴游导览、游记生成三大核心功能，点击直接跳转到专门页面
- **合肥旅游圈**: 基于手绘地图的景点展示，显示景点图片和名称，点击跳转到景点详情页
- **推荐活动**: 展示进行中和即将进行的特色活动
- **游玩路线推荐**: 五大精选路线横向展示，包括亲子游、文化游、生态游、骆岗游、康养游

### 🔍 探索
- **景点**: 景点信息展示，支持类型、距离、名称筛选
- **美食**: 美食推荐，包含类型、距离、人均消费信息
- **酒店**: 住宿信息展示
- **攻略**: 旅游攻略分享
- **游记**: 用户游记展示，包含观看次数和点赞数

### 🤖 伴游助手
- **智能问答**: AI驱动的旅游咨询服务
- **常见问题**: 预设快速问题，一键获取答案
- **语音交互**: 支持语音输入和播报

### 📅 行程
- **行程规划**: 调用智能路径规划功能
- **个人行程管理**: 查看、编辑、删除个人行程
- **行程详情**: 详细的行程安排和费用信息

### 👤 我的
- **个人信息**: 用户资料和统计数据
- **我的游记**: 个人发布的游记管理
- **我的订单**: 通过平台支付的订单管理，支持二维码查看

## 核心功能详解

### 🗺️ 路径规划（独立页面）
- **合肥手绘地图背景**: 真实地图展示，路线可视化
- **推荐路线**:
  - 两日亲子游（动物园、科技馆、融创乐园）
  - 三天文化游（三河古镇、三国遗址公园、渡江战役纪念馆、包公园）
  - 两天生态休闲（环巢湖）
  - 一天骆岗旅游
  - 两天康养度假（庐江山水）

- **AI智能规划**:
  - 用户输入游玩时间、人数、类型、预算、特殊需求
  - 自动生成包含起点、途经点、终点的完整路线
  - 提供游玩时间、介绍、标签、费用信息
  - 支持途经点删除和新增
  - 一键付费和行程保存

- **路线详情展示**:
  - 地图上显示所有景点
  - 底部横向展示途经点信息（图片、名称、标签、费用）
  - 支持滑动查看，可编辑路线

### 🎧 伴游导览（独立页面）
- **手绘地图导览**: 结合合肥手绘地图，实时位置展示
- **智能景点识别**: 进入景点时地图图标闪烁，点击自动语音播报
- **位置服务**: 基于当前位置展示附近景点距离和交通方式
- **语音交互**: 语音查询最近场馆、景点、设施
- **多种交通路线**: 步行、公交、驾车等不同交通工具的最优路线推荐
- **快速查询**: 一键查找附近餐厅、厕所、停车场、地铁站

### 📝 游记生成（独立页面）
- **多图片上传**: 支持上传多张照片，实时预览
- **文字描述输入**: 大文本框输入旅行体验和感受
- **AI智能生成**: 基于上传图片和文字描述生成完整游记
- **多种风格选择**: 文艺风、实用攻略、趣味日记、简约风
- **游记设置**: 自定义标题、标签等
- **游记编辑**: 生成后支持修改、保存、分享功能

## 技术特点

- **响应式设计**: 适配移动端设备
- **现代UI**: 蓝绿白配色方案，简洁美观
- **交互丰富**: 轮播、模态框、标签切换等交互元素
- **功能完整**: 所有按钮和链接都可点击，逻辑清晰
- **实景图片**: 使用高质量实景图片增强视觉效果

## 文件结构

```
├── index.html          # 主应用文件
├── test.html           # 功能测试页面
└── README.md           # 项目说明文档
```

## 使用方法

1. **直接访问**: 在浏览器中打开 `index.html` 文件
2. **功能测试**: 打开 `test.html` 进行功能验证
3. **移动端体验**: 在移动设备或浏览器开发者工具的移动模式下访问

## 主要页面

### 首页 (Home)
- Banner轮播展示三大片区
- 智能服务快速入口
- 旅游圈地图交互
- 推荐活动展示

### 探索 (Explore)
- 景点、美食、酒店、攻略、游记五大分类
- 筛选和搜索功能
- 详细信息展示

### 伴游助手 (Assistant)
- AI聊天界面
- 常见问题快速回复
- 语音交互支持

### 行程 (Itinerary)
- 行程规划工具
- 个人行程管理
- 行程详情查看

### 我的 (Profile)
- 个人信息展示
- 游记和订单管理
- 应用设置

## 特色亮点

1. **智能化**: AI驱动的路径规划和游记生成
2. **个性化**: 基于用户需求的定制化服务
3. **实用性**: 涵盖旅游全流程的功能设计
4. **易用性**: 直观的界面和流畅的交互体验
5. **完整性**: 从规划到记录的闭环服务

## 技术栈

- **前端**: HTML5, CSS3, JavaScript (ES6+)
- **样式**: CSS Grid, Flexbox, CSS动画
- **交互**: 原生JavaScript事件处理
- **图片**: Unsplash高质量图片资源

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 开发说明

该应用采用纯前端技术开发，无需后端服务器，可直接在浏览器中运行。所有交互功能都已实现，包括：

- ✅ 页面导航切换
- ✅ 轮播图自动播放
- ✅ 模态框显示/隐藏
- ✅ 标签页切换
- ✅ 聊天消息发送
- ✅ 地图点击交互
- ✅ 表单输入处理
- ✅ 按钮点击响应

## 未来扩展

- 集成真实地图API
- 连接后端数据库
- 添加用户认证系统
- 集成支付功能
- 添加社交分享功能
- 支持离线使用

---

**注意**: 这是一个演示版本，部分功能（如支付、实时定位等）需要在实际部署时集成相应的API服务。
