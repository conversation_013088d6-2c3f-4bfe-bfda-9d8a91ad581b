<template>
  <div class="container">
    <!-- 顶部导航 -->
    <div class="header">
      <h1>合肥文旅助手</h1>
      <div class="location">
        <span>合肥市</span>
      </div>
    </div>

    <!-- Banner轮播 -->
    <div class="banner">
      <div class="banner-slider" :style="{ transform: sliderTransform }">
        <div 
          class="banner-item" 
          v-for="(slide, index) in slides" 
          :key="index"
          :style="{ backgroundImage: 'linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), url(' + slide.image + ')' }"
        ></div>
      </div>
      <div class="banner-content">
        <h2 class="banner-title">{{ slides[currentSlide].title }}</h2>
        <p class="banner-subtitle">{{ slides[currentSlide].subtitle }}</p>
      </div>
      <div class="banner-dots">
        <div 
          class="dot" 
          v-for="(slide, index) in slides" 
          :key="index"
          :class="{ active: currentSlide === index }"
          @click="goToSlide(index)"
        ></div>
      </div>
    </div>

    <!-- 智能服务 -->
    <smart-services 
      :services="services"
      @service-selected="handleServiceSelected"
    />
  </div>
</template>

<script>
import SmartServices from './SmartServices.vue';

export default {
  name: 'TourismApp',
  components: {
    SmartServices
  },
  data() {
    return {
      services: [
        { id: 1, name: '路线规划', icon: 'fas fa-route' },
        { id: 2, name: '导游服务', icon: 'fas fa-map-marked-alt' },
        { id: 3, name: '旅行笔记', icon: 'fas fa-book-open' },
        { id: 4, name: '美食推荐', icon: 'fas fa-utensils' },
        { id: 5, name: '交通查询', icon: 'fas fa-bus' },
        { id: 6, name: '景点预约', icon: 'fas fa-ticket-alt' }
      ],
      currentSlide: 0,
      slides: [
        {
          title: '合肥欢迎您',
          subtitle: '探索合肥文化与美景',
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800'
        },
        {
          title: '自然风光',
          subtitle: '发现合肥的自然之美',
          image: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800'
        },
        {
          title: '城市景观',
          subtitle: '体验现代合肥',
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800'
        }
      ],
      interval: null
    }
  },
  mounted() {
    this.startAutoPlay();
  },
  beforeDestroy() {
    clearInterval(this.interval);
  },
  methods: {
    startAutoPlay() {
      this.interval = setInterval(() => {
        this.nextSlide();
      }, 3000);
    },
    nextSlide() {
      this.currentSlide = (this.currentSlide + 1) % this.slides.length;
    },
    prevSlide() {
      this.currentSlide = (this.currentSlide - 1 + this.slides.length) % this.slides.length;
    },
    goToSlide(index) {
      this.currentSlide = index;
    },
    handleServiceSelected(service) {
      this.$router.push({
        name: 'ServiceDetail',
        params: { id: service.id },
        props: { service }
      });
    }
  },
  computed: {
    sliderTransform() {
      return `translateX(-${this.currentSlide * 100}%)`;
    }
  }
}
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #e3f2fd 0%, #f1f8e9 100%);
  color: #333;
}

.container {
  max-width: 414px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

/* 顶部导航 */
.header {
  background: linear-gradient(135deg, #2196f3, #4caf50);
  color: white;
  padding: 20px 16px 16px;
  position: relative;
}

.header h1 {
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.location {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 8px;
  font-size: 14px;
  opacity: 0.9;
}

/* Banner轮播 */
.banner {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.banner-slider {
  display: flex;
  transition: transform 0.3s ease;
  height: 100%;
}

.banner-item {
  min-width: 100%;
  height: 100%;
  position: relative;
  background-size: cover;
  background-position: center;
}

.banner-item.slide1 {
  background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), 
    url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800');
}

.banner-item.slide2 {
  background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), 
    url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800');
}

.banner-item.slide3 {
  background-image: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.3)), 
    url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800');
}

.banner-content {
  position: absolute;
  bottom: 20px;
  left: 20px;
  color: white;
}

.banner-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

.banner-dots {
  position: absolute;
  bottom: 15px;
  right: 20px;
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255,255,255,0.5);
  cursor: pointer;
  transition: background 0.3s;
}

.dot.active {
  background: white;
}
</style>