<template>
  <div class="service-detail">
    <div class="detail-header">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h2>{{ service.name }}</h2>
    </div>
    
    <div class="detail-content">
      <div class="service-icon-large">
        <i :class="service.icon"></i>
      </div>
      
      <div class="service-description">
        <p>这里是关于{{ service.name }}的详细描述...</p>
      </div>
      
      <button class="action-btn" @click="startService">
        开始使用{{ service.name }}
      </button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ServiceDetail',
  props: {
    service: {
      type: Object,
      required: true
    }
  },
  methods: {
    goBack() {
      this.$router.go(-1);
    },
    startService() {
      console.log('Starting service:', this.service.name);
      // 这里添加启动服务的逻辑
    }
  }
}
</script>

<style scoped>
.service-detail {
  max-width: 414px;
  margin: 0 auto;
  background: white;
  min-height: 100vh;
  padding: 20px;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
}

.back-btn {
  background: none;
  border: none;
  font-size: 20px;
  margin-right: 15px;
  cursor: pointer;
  color: #2196f3;
}

.detail-header h2 {
  font-size: 22px;
  color: #333;
}

.service-icon-large {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #2196f3, #4caf50);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
}

.service-description {
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  line-height: 1.6;
}

.action-btn {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #2196f3, #4caf50);
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.action-btn:hover {
  transform: translateY(-2px);
}
</style>