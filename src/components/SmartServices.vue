<template>
  <div class="smart-services">
    <h2 class="section-title">智能服务</h2>
    <div class="services-grid">
      <div 
        class="service-item" 
        v-for="service in services" 
        :key="service.id"
        @click="selectService(service)"
      >
        <div class="service-icon">
          <i :class="service.icon"></i>
        </div>
        <div class="service-name">{{ service.name }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SmartServices',
  props: {
    services: {
      type: Array,
      required: true
    }
  },
  methods: {
    selectService(service) {
      this.$emit('service-selected', service);
    }
  }
}
</script>

<style scoped>
.smart-services {
  padding: 20px 16px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #2196f3;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.service-item {
  background: white;
  border-radius: 12px;
  padding: 16px 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.service-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.service-icon {
  width: 40px;
  height: 40px;
  margin: 0 auto 8px;
  background: linear-gradient(135deg, #2196f3, #4caf50);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.service-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}
</style>