import { createRouter, createWebHistory } from 'vue-router'
import TourismApp from '../components/TourismApp.vue'
import ServiceDetail from '../components/ServiceDetail.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: TourismApp
  },
  {
    path: '/service/:id',
    name: 'ServiceDetail',
    component: ServiceDetail,
    props: true
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router