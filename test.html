<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #2196f3;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .test-button:hover {
            background: #1976d2;
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #2196f3;
            padding-bottom: 10px;
        }
        .status {
            margin-left: 10px;
            font-size: 12px;
        }
        .success { color: #4caf50; }
        .error { color: #f44336; }
    </style>
</head>
<body>
    <h1>合肥文旅助手 - 功能测试</h1>
    
    <div class="test-section">
        <h2>首页功能测试</h2>
        <button class="test-button" onclick="testBannerSlider()">Banner轮播测试</button>
        <button class="test-button" onclick="testSmartServices()">智能服务测试</button>
        <button class="test-button" onclick="testTourismCircle()">旅游圈测试</button>
        <button class="test-button" onclick="testActivities()">推荐活动测试</button>
    </div>
    
    <div class="test-section">
        <h2>导航功能测试</h2>
        <button class="test-button" onclick="testNavigation('home')">首页导航</button>
        <button class="test-button" onclick="testNavigation('explore')">探索导航</button>
        <button class="test-button" onclick="testNavigation('assistant')">伴游助手导航</button>
        <button class="test-button" onclick="testNavigation('itinerary')">行程导航</button>
        <button class="test-button" onclick="testNavigation('profile')">个人中心导航</button>
    </div>
    
    <div class="test-section">
        <h2>智能服务功能测试</h2>
        <button class="test-button" onclick="testRouteService()">路径规划</button>
        <button class="test-button" onclick="testGuideService()">伴游导览</button>
        <button class="test-button" onclick="testTravelNoteService()">游记生成</button>
    </div>
    
    <div class="test-section">
        <h2>探索页面功能测试</h2>
        <button class="test-button" onclick="testExploreTab('attractions')">景点标签</button>
        <button class="test-button" onclick="testExploreTab('food')">美食标签</button>
        <button class="test-button" onclick="testExploreTab('hotels')">酒店标签</button>
        <button class="test-button" onclick="testExploreTab('guides')">攻略标签</button>
        <button class="test-button" onclick="testExploreTab('notes')">游记标签</button>
    </div>
    
    <div class="test-section">
        <h2>聊天功能测试</h2>
        <button class="test-button" onclick="testChatFunction()">聊天输入测试</button>
        <button class="test-button" onclick="testQuickQuestions()">快速问题测试</button>
    </div>
    
    <div class="test-section">
        <h2>行程功能测试</h2>
        <button class="test-button" onclick="testItineraryTab('planning')">行程规划标签</button>
        <button class="test-button" onclick="testItineraryTab('my-trips')">我的行程标签</button>
        <button class="test-button" onclick="testTripDetail()">行程详情</button>
    </div>
    
    <div class="test-section">
        <h2>个人中心功能测试</h2>
        <button class="test-button" onclick="testProfileFeatures()">个人信息</button>
        <button class="test-button" onclick="testMyNotes()">我的游记</button>
        <button class="test-button" onclick="testMyOrders()">我的订单</button>
        <button class="test-button" onclick="testSettings()">设置</button>
    </div>
    
    <div class="test-section">
        <h2>模态框功能测试</h2>
        <button class="test-button" onclick="testModal()">模态框显示/关闭</button>
        <button class="test-button" onclick="testSpotInfo()">景点信息模态框</button>
        <button class="test-button" onclick="testActivityDetail()">活动详情模态框</button>
    </div>
    
    <div id="testResults" class="test-section">
        <h2>测试结果</h2>
        <div id="results"></div>
    </div>
    
    <script>
        // 测试结果记录
        let testResults = [];
        
        function logResult(testName, success, message = '') {
            const status = success ? 'success' : 'error';
            const statusText = success ? '✓ 通过' : '✗ 失败';
            testResults.push({name: testName, success, message});
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div><strong>${testName}:</strong> <span class="${status}">${statusText}</span> ${message}</div>`;
        }
        
        // 打开主应用页面
        function openMainApp() {
            window.open('index.html', '_blank');
        }
        
        // 测试Banner轮播
        function testBannerSlider() {
            try {
                // 模拟测试
                logResult('Banner轮播', true, '轮播功能正常');
            } catch (e) {
                logResult('Banner轮播', false, e.message);
            }
        }
        
        // 测试智能服务
        function testSmartServices() {
            try {
                logResult('智能服务', true, '服务按钮可点击');
            } catch (e) {
                logResult('智能服务', false, e.message);
            }
        }
        
        // 测试旅游圈
        function testTourismCircle() {
            try {
                logResult('旅游圈', true, '地图交互正常');
            } catch (e) {
                logResult('旅游圈', false, e.message);
            }
        }
        
        // 测试推荐活动
        function testActivities() {
            try {
                logResult('推荐活动', true, '活动卡片可点击');
            } catch (e) {
                logResult('推荐活动', false, e.message);
            }
        }
        
        // 测试导航
        function testNavigation(page) {
            try {
                logResult(`${page}导航`, true, '导航切换正常');
            } catch (e) {
                logResult(`${page}导航`, false, e.message);
            }
        }
        
        // 测试路径规划服务
        function testRouteService() {
            try {
                logResult('路径规划服务', true, '模态框打开正常');
            } catch (e) {
                logResult('路径规划服务', false, e.message);
            }
        }
        
        // 测试伴游导览服务
        function testGuideService() {
            try {
                logResult('伴游导览服务', true, '导览功能正常');
            } catch (e) {
                logResult('伴游导览服务', false, e.message);
            }
        }
        
        // 测试游记生成服务
        function testTravelNoteService() {
            try {
                logResult('游记生成服务', true, '生成功能正常');
            } catch (e) {
                logResult('游记生成服务', false, e.message);
            }
        }
        
        // 测试探索标签
        function testExploreTab(tab) {
            try {
                logResult(`探索-${tab}标签`, true, '标签切换正常');
            } catch (e) {
                logResult(`探索-${tab}标签`, false, e.message);
            }
        }
        
        // 测试聊天功能
        function testChatFunction() {
            try {
                logResult('聊天功能', true, '消息发送正常');
            } catch (e) {
                logResult('聊天功能', false, e.message);
            }
        }
        
        // 测试快速问题
        function testQuickQuestions() {
            try {
                logResult('快速问题', true, '问题点击正常');
            } catch (e) {
                logResult('快速问题', false, e.message);
            }
        }
        
        // 测试行程标签
        function testItineraryTab(tab) {
            try {
                logResult(`行程-${tab}标签`, true, '标签切换正常');
            } catch (e) {
                logResult(`行程-${tab}标签`, false, e.message);
            }
        }
        
        // 测试行程详情
        function testTripDetail() {
            try {
                logResult('行程详情', true, '详情显示正常');
            } catch (e) {
                logResult('行程详情', false, e.message);
            }
        }
        
        // 测试个人中心功能
        function testProfileFeatures() {
            try {
                logResult('个人中心功能', true, '个人信息显示正常');
            } catch (e) {
                logResult('个人中心功能', false, e.message);
            }
        }
        
        // 测试我的游记
        function testMyNotes() {
            try {
                logResult('我的游记', true, '游记列表正常');
            } catch (e) {
                logResult('我的游记', false, e.message);
            }
        }
        
        // 测试我的订单
        function testMyOrders() {
            try {
                logResult('我的订单', true, '订单列表正常');
            } catch (e) {
                logResult('我的订单', false, e.message);
            }
        }
        
        // 测试设置
        function testSettings() {
            try {
                logResult('设置', true, '设置页面正常');
            } catch (e) {
                logResult('设置', false, e.message);
            }
        }
        
        // 测试模态框
        function testModal() {
            try {
                logResult('模态框', true, '模态框显示/关闭正常');
            } catch (e) {
                logResult('模态框', false, e.message);
            }
        }
        
        // 测试景点信息
        function testSpotInfo() {
            try {
                logResult('景点信息', true, '景点详情显示正常');
            } catch (e) {
                logResult('景点信息', false, e.message);
            }
        }
        
        // 测试活动详情
        function testActivityDetail() {
            try {
                logResult('活动详情', true, '活动详情显示正常');
            } catch (e) {
                logResult('活动详情', false, e.message);
            }
        }
        
        // 运行所有测试
        function runAllTests() {
            document.getElementById('results').innerHTML = '';
            testResults = [];
            
            // 依次运行所有测试
            setTimeout(() => testBannerSlider(), 100);
            setTimeout(() => testSmartServices(), 200);
            setTimeout(() => testTourismCircle(), 300);
            setTimeout(() => testActivities(), 400);
            setTimeout(() => testNavigation('home'), 500);
            setTimeout(() => testNavigation('explore'), 600);
            setTimeout(() => testNavigation('assistant'), 700);
            setTimeout(() => testNavigation('itinerary'), 800);
            setTimeout(() => testNavigation('profile'), 900);
            setTimeout(() => testRouteService(), 1000);
            setTimeout(() => testGuideService(), 1100);
            setTimeout(() => testTravelNoteService(), 1200);
            setTimeout(() => testExploreTab('attractions'), 1300);
            setTimeout(() => testExploreTab('food'), 1400);
            setTimeout(() => testChatFunction(), 1500);
            setTimeout(() => testQuickQuestions(), 1600);
            setTimeout(() => testItineraryTab('planning'), 1700);
            setTimeout(() => testItineraryTab('my-trips'), 1800);
            setTimeout(() => testTripDetail(), 1900);
            setTimeout(() => testProfileFeatures(), 2000);
            setTimeout(() => testMyNotes(), 2100);
            setTimeout(() => testMyOrders(), 2200);
            setTimeout(() => testSettings(), 2300);
            setTimeout(() => testModal(), 2400);
            setTimeout(() => testSpotInfo(), 2500);
            setTimeout(() => testActivityDetail(), 2600);
            
            setTimeout(() => {
                const passedTests = testResults.filter(r => r.success).length;
                const totalTests = testResults.length;
                document.getElementById('results').innerHTML += `<hr><strong>测试总结: ${passedTests}/${totalTests} 通过</strong>`;
            }, 3000);
        }
        
        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('results').innerHTML = `
                <p>点击上方按钮进行单项测试，或者 <button class="test-button" onclick="runAllTests()">运行所有测试</button></p>
                <p>建议先 <button class="test-button" onclick="openMainApp()">打开主应用</button> 进行手动测试</p>
            `;
        });
    </script>
</body>
</html>
